<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Coin Flip Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        #coinCanvas {
            border: 2px solid #444;
            border-radius: 10px;
            background: #222;
            margin: 20px 0;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }
        
        button {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .flip-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }
        
        .flip-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .flip-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .result {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            padding: 15px 30px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            min-height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .heads {
            color: #ffd700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        .tails {
            color: #c0c0c0;
            text-shadow: 0 0 10px rgba(192, 192, 192, 0.5);
        }
        
        .info {
            text-align: center;
            margin: 20px 0;
            color: #aaa;
        }
        
        .enhancement-info {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            max-width: 600px;
        }
        
        .enhancement-info h3 {
            color: #4ade80;
            margin-top: 0;
        }
        
        .enhancement-info ul {
            text-align: left;
            color: #d1d5db;
        }
    </style>
</head>
<body>
    <h1>Enhanced Coin Flip Animation Test</h1>
    
    <div class="enhancement-info">
        <h3>🚀 Enhancements Applied:</h3>
        <ul>
            <li><strong>Increased Spin Speed:</strong> 12-18 rotations (vs 6 previously) with randomized timing</li>
            <li><strong>Improved Camera Angle:</strong> Positioned at (0, 0.2, 2.5) for optimal viewing</li>
            <li><strong>Enhanced Final Display:</strong> Settling animation with subtle wobble effect</li>
            <li><strong>Better Physics:</strong> More realistic damping and bounce behavior</li>
            <li><strong>Visual Emphasis:</strong> Lighting and scale effects for final result</li>
        </ul>
    </div>
    
    <canvas id="coinCanvas" width="400" height="400"></canvas>
    
    <div class="controls">
        <button class="flip-btn" onclick="flipCoin()">🪙 Flip Coin (Random)</button>
        <button class="flip-btn" onclick="flipCoin('heads')">👑 Force Heads</button>
        <button class="flip-btn" onclick="flipCoin('tails')">⚡ Force Tails</button>
        <button class="flip-btn" onclick="resetCoin()">🔄 Reset</button>
    </div>
    
    <div class="result" id="result">Click "Flip Coin" to start!</div>
    
    <div class="info">
        <p>This test demonstrates the enhanced coin flip animation with:</p>
        <p><strong>Fast spinning</strong> during upward motion • <strong>Clear final result</strong> display • <strong>Optimal camera angle</strong></p>
    </div>

    <!-- Load Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Load our coin flipper module -->
    <script src="src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper;
        let isFlipping = false;
        
        // Initialize the coin flipper
        async function initCoinFlipper() {
            try {
                coinFlipper = new CoinFlipper('coinCanvas', {
                    enableSound: true,
                    idleSpeed: 0.02
                });
                
                await coinFlipper.ready();
                await coinFlipper.startIdle();
                
                console.log('✅ Coin flipper initialized successfully');
                updateResult('Ready to flip! 🎯');
            } catch (error) {
                console.error('❌ Failed to initialize coin flipper:', error);
                updateResult('❌ Failed to initialize');
            }
        }
        
        // Flip the coin
        async function flipCoin(forceResult = null) {
            if (isFlipping || !coinFlipper) return;
            
            isFlipping = true;
            updateButtons(true);
            updateResult('🌪️ Flipping...');
            
            try {
                const result = await coinFlipper.toss(forceResult, true);
                
                // Display result with styling
                const resultText = result === 'heads' ? '👑 HEADS!' : '⚡ TAILS!';
                const resultClass = result === 'heads' ? 'heads' : 'tails';
                updateResult(resultText, resultClass);
                
                console.log('🎯 Flip result:', result);
                
            } catch (error) {
                console.error('❌ Flip error:', error);
                updateResult('❌ Flip failed');
            } finally {
                isFlipping = false;
                updateButtons(false);
            }
        }
        
        // Reset the coin
        async function resetCoin() {
            if (isFlipping || !coinFlipper) return;
            
            try {
                await coinFlipper.resetCoin();
                await coinFlipper.startIdle();
                updateResult('🔄 Reset complete');
                console.log('🔄 Coin reset');
            } catch (error) {
                console.error('❌ Reset error:', error);
            }
        }
        
        // Update result display
        function updateResult(text, className = '') {
            const resultEl = document.getElementById('result');
            resultEl.textContent = text;
            resultEl.className = 'result ' + className;
        }
        
        // Update button states
        function updateButtons(disabled) {
            const buttons = document.querySelectorAll('.flip-btn');
            buttons.forEach(btn => {
                btn.disabled = disabled;
            });
        }
        
        // Initialize when page loads
        window.addEventListener('load', initCoinFlipper);
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (coinFlipper) {
                coinFlipper.resize();
            }
        });
    </script>
</body>
</html>
